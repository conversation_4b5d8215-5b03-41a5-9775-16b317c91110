import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Github, Youtube, Linkedin, Mail, Twitter, Globe, BarChart, Leaf, Music } from "lucide-react";
import { motion } from "framer-motion";

export default function Linktree() {
  const links = [
    { href: "https://github.com/aliahmad1967", label: "GitHub", icon: <Github /> },
    { href: "https://public.tableau.com/app/profile/ali2526/vizzes", label: "Tableau Portfolio", icon: <BarChart /> },
    { href: "https://www.youtube.com/@nidalahmad6100", label: "YouTube", icon: <Youtube /> },
    { href: "https://huggingface.co/Ali1967", label: "Hugging Face", icon: <Globe /> },
    { href: "https://www.tiktok.com/@nidalahmad7", label: "TikTok", icon: <Music /> },
    { href: "https://x.com/nidal5nidal", label: "X (Twitter)", icon: <Twitter /> },
    { href: "https://www.linkedin.com/in/nidal-ahmad-a77881260", label: "LinkedIn", icon: <Linkedin /> },
    { href: "mailto:<EMAIL>", label: "Email", icon: <Mail /> },
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-black via-gray-900 to-black text-white p-6">
      {/* Logo */}
      <motion.img
        src="/ansari-logo.png"
        alt="Ansari Logo"
        className="w-32 h-32 mb-6"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.8 }}
      />

      {/* Title */}
      <motion.h1
        className="text-3xl font-bold mb-2 flex items-center gap-2"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        AL-ANSARI <Leaf className="text-green-500" />
      </motion.h1>

      <p className="text-gray-400 mb-8 text-center max-w-md">
        Welcome to my creative hub! Explore my work, projects, and insights across different platforms.
      </p>

      {/* Links */}
      <div className="w-full max-w-md flex flex-col gap-4">
        {links.map((link, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 * index }}
          >
            <Card className="bg-gray-800/80 backdrop-blur-md border border-gray-700 shadow-lg hover:shadow-green-500/40 transition duration-300">
              <CardContent className="p-0">
                <a
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-4 p-4 text-lg font-medium hover:text-green-400 transition"
                >
                  {link.icon}
                  {link.label}
                </a>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Footer */}
      <p className="text-gray-500 mt-10 text-sm">
        © {new Date().getFullYear()} AL-ANSARI | Culture & Information
      </p>
    </div>
  );
}
