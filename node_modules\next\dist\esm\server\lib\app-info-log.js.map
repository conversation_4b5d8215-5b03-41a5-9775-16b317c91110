{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "names": ["loadEnvConfig", "Log", "bold", "purple", "PHASE_DEVELOPMENT_SERVER", "loadConfig", "getEnabledExperimentalFeatures", "logStartInfo", "networkUrl", "appUrl", "envInfo", "expFeatureInfo", "maxExperimentalFeatures", "bootstrap", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "TURBOPACK", "length", "join", "exp", "slice", "info", "getStartServerInfo", "dir", "onLoadUserConfig", "userConfig", "userNextConfigExperimental", "experimental", "sort", "a", "b", "loadedEnvFiles", "console", "map", "f", "path"], "mappings": "AAAA,SAASA,aAAa,QAAQ,YAAW;AACzC,YAAYC,SAAS,yBAAwB;AAC7C,SAASC,IAAI,EAAEC,MAAM,QAAQ,uBAAsB;AACnD,SAASC,wBAAwB,QAAQ,6BAA4B;AACrE,OAAOC,cAAcC,8BAA8B,QAAQ,YAAW;AAEtE,OAAO,SAASC,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,cAAc,EACdC,uBAAuB,EAOxB;IACCX,IAAIY,SAAS,CACXX,KACEC,OACE,CAAC,CAAC,EAAEF,IAAIa,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,CAAC,EAC3DF,QAAQC,GAAG,CAACE,SAAS,GAAG,aAAa,GACtC,CAAC;IAIR,IAAIV,QAAQ;QACVR,IAAIY,SAAS,CAAC,CAAC,iBAAiB,EAAEJ,OAAO,CAAC;IAC5C;IACA,IAAID,YAAY;QACdP,IAAIY,SAAS,CAAC,CAAC,iBAAiB,EAAEL,WAAW,CAAC;IAChD;IACA,IAAIE,2BAAAA,QAASU,MAAM,EAAEnB,IAAIY,SAAS,CAAC,CAAC,iBAAiB,EAAEH,QAAQW,IAAI,CAAC,MAAM,CAAC;IAE3E,IAAIV,kCAAAA,eAAgBS,MAAM,EAAE;QAC1BnB,IAAIY,SAAS,CAAC,CAAC,sCAAsC,CAAC;QACtD,4BAA4B;QAC5B,KAAK,MAAMS,OAAOX,eAAeY,KAAK,CAAC,GAAGX,yBAA0B;YAClEX,IAAIY,SAAS,CAAC,CAAC,KAAK,EAAES,IAAI,CAAC;QAC7B;QACA,qCAAqC,GACrC,IAAIX,eAAeS,MAAM,GAAG,KAAKR,yBAAyB;YACxDX,IAAIY,SAAS,CAAC,CAAC,QAAQ,CAAC;QAC1B;IACF;IAEA,oCAAoC;IACpCZ,IAAIuB,IAAI,CAAC;AACX;AAEA,OAAO,eAAeC,mBAAmBC,GAAW;IAIlD,IAAIf,iBAA2B,EAAE;IACjC,MAAMN,WAAWD,0BAA0BsB,KAAK;QAC9CC,kBAAiBC,UAAU;YACzB,MAAMC,6BAA6BvB,+BACjCsB,WAAWE,YAAY;YAEzBnB,iBAAiBkB,2BAA2BE,IAAI,CAC9C,CAACC,GAAGC,IAAMD,EAAEZ,MAAM,GAAGa,EAAEb,MAAM;QAEjC;IACF;IAEA,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAIV,UAAoB,EAAE;IAC1B,MAAM,EAAEwB,cAAc,EAAE,GAAGlC,cAAc0B,KAAK,MAAMS,SAAS;IAC7D,IAAID,eAAed,MAAM,GAAG,GAAG;QAC7BV,UAAUwB,eAAeE,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACL5B;QACAC;IACF;AACF"}