export const ACTION_REFRESH = "refresh";
export const ACTION_NAVIGATE = "navigate";
export const ACTION_RESTORE = "restore";
export const ACTION_SERVER_PATCH = "server-patch";
export const ACTION_PREFETCH = "prefetch";
export const ACTION_FAST_REFRESH = "fast-refresh";
export const ACTION_SERVER_ACTION = "server-action";
export var PrefetchKind;
(function(PrefetchKind) {
    PrefetchKind["AUTO"] = "auto";
    PrefetchKind["FULL"] = "full";
    PrefetchKind["TEMPORARY"] = "temporary";
})(PrefetchKind || (PrefetchKind = {}));
export function isThenable(value) {
    // TODO: We don't gain anything from this abstraction. It's unsound, and only
    // makes sense in the specific places where we use it. So it's better to keep
    // the type coercion inline, instead of leaking this to other places in
    // the codebase.
    return value && (typeof value === "object" || typeof value === "function") && typeof value.then === "function";
}

//# sourceMappingURL=router-reducer-types.js.map