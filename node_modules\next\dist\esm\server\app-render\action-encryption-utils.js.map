{"version": 3, "sources": ["../../../src/server/app-render/action-encryption-utils.ts"], "names": ["__next_loaded_action_key", "__next_internal_development_raw_action_key", "arrayBufferToString", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "stringToUint8Array", "length", "arr", "charCodeAt", "encrypt", "key", "iv", "data", "crypto", "subtle", "name", "decrypt", "generateRandomActionKeyRaw", "dev", "<PERSON><PERSON>ey", "exported", "exportKey", "b64", "btoa", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "setReferenceManifestsSingleton", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "getServerModuleMap", "serverActionsManifestSingleton", "Error", "getClientReferenceManifestSingleton", "getActionEncryptionKey", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob"], "mappings": "AAGA,wFAAwF;AACxF,mCAAmC;AACnC,IAAIA;AACJ,IAAIC;AAEJ,OAAO,SAASC,oBAAoBC,MAAmB;IACrD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEA,OAAO,SAASE,mBAAmBF,MAAc;IAC/C,MAAML,MAAMK,OAAOG,MAAM;IACzB,MAAMC,MAAM,IAAIV,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BG,GAAG,CAACH,EAAE,GAAGD,OAAOK,UAAU,CAACJ;IAC7B;IAEA,OAAOG;AACT;AAEA,OAAO,SAASE,QAAQC,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACL,OAAO,CAC1B;QACEM,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,OAAO,SAASI,QAAQN,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACE,OAAO,CAC1B;QACED,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,OAAO,eAAeK,2BAA2BC,GAAa;IAC5D,mEAAmE;IACnE,4BAA4B;IAC5B,IAAIA,KAAK;QACP,IAAI,OAAOzB,+CAA+C,aAAa;YACrE,OAAOA;QACT;IACF;IAEA,MAAMiB,MAAM,MAAMG,OAAOC,MAAM,CAACK,WAAW,CACzC;QACEJ,MAAM;QACNT,QAAQ;IACV,GACA,MACA;QAAC;QAAW;KAAU;IAExB,MAAMc,WAAW,MAAMP,OAAOC,MAAM,CAACO,SAAS,CAAC,OAAOX;IACtD,MAAMY,MAAMC,KAAK7B,oBAAoB0B;IAErC5B,2BAA2BkB;IAC3B,IAAIQ,KAAK;QACPzB,6CAA6C6B;IAC/C;IAEA,OAAOA;AACT;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAME,oCAAoCC,OAAOC,GAAG,CAClD;AAGF,OAAO,SAASC,+BAA+B,EAC7CC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAWhB;IACC,aAAa;IACbC,UAAU,CAACP,kCAAkC,GAAG;QAC9CI;QACAC;QACAC;IACF;AACF;AAEA,OAAO,SAASE;IACd,MAAMC,iCAAiC,AAACF,UAAkB,CACxDP,kCACD;IAUD,IAAI,CAACS,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BH,eAAe;AACvD;AAEA,OAAO,SAASK;IACd,MAAMF,iCAAiC,AAACF,UAAkB,CACxDP,kCACD;IAKD,IAAI,CAACS,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,OAAOD,+BAA+BL,uBAAuB;AAC/D;AAEA,OAAO,eAAeQ;IACpB,IAAI5C,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAMyC,iCAAiC,AAACF,UAAkB,CACxDP,kCACD;IAKD,IAAI,CAACS,gCAAgC;QACnC,MAAM,IAAIC,MACR;IAEJ;IAEA,MAAMG,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CP,+BAA+BJ,qBAAqB,CAACY,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,IAAIR,MAAM;IAClB;IAEA1C,2BAA2B,MAAMqB,OAAOC,MAAM,CAAC6B,SAAS,CACtD,OACAtC,mBAAmBuC,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAO7C;AACT"}