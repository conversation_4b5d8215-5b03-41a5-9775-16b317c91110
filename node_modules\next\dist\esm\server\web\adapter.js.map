{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "NextFetchEvent", "NextRequest", "NextResponse", "relativizeURL", "waitUntilSymbol", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_PARAMETERS", "NEXT_QUERY_PARAM_PREFIX", "ensureInstrumentationRegistered", "RequestAsyncStorageWrapper", "requestAsyncStorage", "getTracer", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "self", "__BUILD_MANIFEST", "prerenderManifest", "__PRERENDER_MANIFEST", "JSON", "parse", "url", "requestUrl", "nextConfig", "searchParams", "value", "getAll", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isDataReq", "pathname", "requestHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "previewModeId", "event", "response", "cookiesFromResponse", "isMiddleware", "wrap", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "previewModeEncryptionKey", "previewModeSigningKey", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "nextUrl", "String", "relativizedRewrite", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "fetchMetrics"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,QAAQ,UAAS;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SAASC,iBAAiB,QAAQ,6CAA4C;AAC9E,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAG/C,MAAMC,wBAAwBb;IAI5Bc,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIvB,mBAAmB;YAAEsB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAG,cAAc;QACZ,MAAM,IAAIxB,mBAAmB;YAAEsB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,YAAY;QACV,MAAM,IAAIzB,mBAAmB;YAAEsB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAASrB;IACf,OAAOqB,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;IAEtBqB;IACA,MAAM3B;IAEN,yCAAyC;IACzC,MAAMmC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IACzD,MAAMC,oBACJ,OAAOF,KAAKG,oBAAoB,KAAK,WACjCC,KAAKC,KAAK,CAACL,KAAKG,oBAAoB,IACpClB;IAENf,OAAOK,OAAO,CAAC+B,GAAG,GAAG7C,gBAAgBS,OAAOK,OAAO,CAAC+B,GAAG;IAEvD,MAAMC,aAAa,IAAIhD,QAAQW,OAAOK,OAAO,CAAC+B,GAAG,EAAE;QACjD1B,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM7B,OAAO;WAAI4B,WAAWE,YAAY,CAAC9B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM+B,QAAQH,WAAWE,YAAY,CAACE,MAAM,CAAC3B;QAE7C,IACEA,QAAQrB,2BACRqB,IAAI4B,UAAU,CAACjD,0BACf;YACA,MAAMkD,gBAAgB7B,IAAI8B,SAAS,CAACnD,wBAAwBoD,MAAM;YAClER,WAAWE,YAAY,CAACO,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOP,MAAO;gBACvBH,WAAWE,YAAY,CAACS,MAAM,CAACL,eAAeI;YAChD;YACAV,WAAWE,YAAY,CAACO,MAAM,CAAChC;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMmC,UAAUZ,WAAWY,OAAO;IAClCZ,WAAWY,OAAO,GAAG;IAErB,MAAMC,YAAYlD,OAAOK,OAAO,CAACK,OAAO,CAAC,gBAAgB;IAEzD,IAAIwC,aAAab,WAAWc,QAAQ,KAAK,UAAU;QACjDd,WAAWc,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBrE,4BAA4BiB,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAM2C,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACzB,iBAAiB;QACpB,KAAK,MAAM0B,SAAS/D,kBAAmB;YACrC,MAAMsB,MAAMyC,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMjB,QAAQY,eAAevC,GAAG,CAACC;YACjC,IAAI0B,OAAO;gBACTa,cAAcK,GAAG,CAAC5C,KAAKsC,eAAevC,GAAG,CAACC;gBAC1CsC,eAAeN,MAAM,CAAChC;YACxB;QACF;IACF;IAEA,MAAM6C,eAAerC,QAAQC,GAAG,CAACqC,kCAAkC,GAC/D,IAAIC,IAAI7D,OAAOK,OAAO,CAAC+B,GAAG,IAC1BC;IAEJ,MAAMhC,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOX,0BAA0BqE,cAAc,MAAMH,QAAQ;QAC7DtD,MAAM;YACJ4D,MAAM9D,OAAOK,OAAO,CAACyD,IAAI;YACzBC,KAAK/D,OAAOK,OAAO,CAAC0D,GAAG;YACvBrD,SAAS0C;YACTY,IAAIhE,OAAOK,OAAO,CAAC2D,EAAE;YACrBC,QAAQjE,OAAOK,OAAO,CAAC4D,MAAM;YAC7B3B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;YACrC4B,QAAQlE,OAAOK,OAAO,CAAC6D,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIhB,WAAW;QACbiB,OAAOC,cAAc,CAAC/D,SAAS,YAAY;YACzCgE,YAAY;YACZ7B,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAAC8B,WAAmBC,kBAAkB,IACvC,AAACvE,OAAewE,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5CvE,OACAwE,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAarD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YACtCC,qBAAqBvD,QAAQC,GAAG,CAACuD,6BAA6B;YAC9DC,KAAKzD,QAAQC,GAAG,CAACqD,QAAQ,KAAK;YAC9BxB,gBAAgBpD,OAAOK,OAAO,CAACK,OAAO;YACtCsE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAAS;wBACPC,eAAe;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIxG,eAAe;QAAEqB;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAIqF;IACJ,IAAIC;IAEJD,WAAW,MAAMzE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAMsF,eACJ3F,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAIuF,cAAc;YAChB,OAAOhG,2BAA2BiG,IAAI,CACpChG,qBACA;gBACEiG,KAAKxF;gBACLyF,YAAY;oBACVC,iBAAiB,CAACC;wBAChBN,sBAAsBM;oBACxB;oBACA,2EAA2E;oBAC3EC,cAAcjE,CAAAA,qCAAAA,kBAAmBsD,OAAO,KAAI;wBAC1CC,eAAe;wBACfW,0BAA0B;wBAC1BC,uBAAuB;oBACzB;gBACF;YACF,GACA,IAAMnG,OAAOoG,OAAO,CAAC/F,SAASmF;QAElC;QACA,OAAOxF,OAAOoG,OAAO,CAAC/F,SAASmF;IACjC;IAEA,yCAAyC;IACzC,IAAIC,YAAY,CAAEA,CAAAA,oBAAoBY,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIb,YAAYC,qBAAqB;QACnCD,SAAS/E,OAAO,CAACgD,GAAG,CAAC,cAAcgC;IACrC;IAEA;;;;;GAKC,GACD,MAAMa,UAAUd,4BAAAA,SAAU/E,OAAO,CAACG,GAAG,CAAC;IACtC,IAAI4E,YAAYc,SAAS;QACvB,MAAMC,aAAa,IAAInH,QAAQkH,SAAS;YACtCE,aAAa;YACb/F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA,IAAI,CAAChB,QAAQC,GAAG,CAACqC,kCAAkC,EAAE;YACnD,IAAI4C,WAAWE,IAAI,KAAKrG,QAAQsG,OAAO,CAACD,IAAI,EAAE;gBAC5CF,WAAWvD,OAAO,GAAGA,WAAWuD,WAAWvD,OAAO;gBAClDwC,SAAS/E,OAAO,CAACgD,GAAG,CAAC,wBAAwBkD,OAAOJ;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMK,qBAAqB1H,cACzByH,OAAOJ,aACPI,OAAOvE;QAGT,IACEa,aACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE5B,CAAAA,QAAQC,GAAG,CAACuF,0CAA0C,IACtDD,mBAAmBE,KAAK,CAAC,gBAAe,GAE1C;YACAtB,SAAS/E,OAAO,CAACgD,GAAG,CAAC,oBAAoBmD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMG,WAAWvB,4BAAAA,SAAU/E,OAAO,CAACG,GAAG,CAAC;IACvC,IAAI4E,YAAYuB,YAAY,CAACnF,iBAAiB;QAC5C,MAAMoF,cAAc,IAAI5H,QAAQ2H,UAAU;YACxCP,aAAa;YACb/F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/B4B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA;;;KAGC,GACDmD,WAAW,IAAIY,SAASZ,SAAS3B,IAAI,EAAE2B;QAEvC,IAAI,CAACnE,QAAQC,GAAG,CAACqC,kCAAkC,EAAE;YACnD,IAAIqD,YAAYP,IAAI,KAAKrG,QAAQsG,OAAO,CAACD,IAAI,EAAE;gBAC7CO,YAAYhE,OAAO,GAAGA,WAAWgE,YAAYhE,OAAO;gBACpDwC,SAAS/E,OAAO,CAACgD,GAAG,CAAC,YAAYkD,OAAOK;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAI/D,WAAW;YACbuC,SAAS/E,OAAO,CAACoC,MAAM,CAAC;YACxB2C,SAAS/E,OAAO,CAACgD,GAAG,CAClB,qBACAvE,cAAcyH,OAAOK,cAAcL,OAAOvE;QAE9C;IACF;IAEA,MAAM6E,gBAAgBzB,WAAWA,WAAWvG,aAAaiI,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAcxG,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMwG,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACtG,KAAK0B,MAAM,IAAIa,cAAe;YACxC6D,cAAcxG,OAAO,CAACgD,GAAG,CAAC,CAAC,qBAAqB,EAAE5C,IAAI,CAAC,EAAE0B;YACzD6E,mBAAmBC,IAAI,CAACxG;QAC1B;QAEA,IAAIuG,mBAAmBxE,MAAM,GAAG,GAAG;YACjCqE,cAAcxG,OAAO,CAACgD,GAAG,CACvB,iCACA0D,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACL9B,UAAUyB;QACV3G,WAAWiH,QAAQC,GAAG,CAACjC,KAAK,CAACpG,gBAAgB;QAC7CsI,cAAcrH,QAAQqH,YAAY;IACpC;AACF"}