{"version": 3, "sources": ["../../../../src/shared/lib/router/adapters.tsx"], "names": ["React", "useMemo", "useRef", "PathnameContext", "isDynamicRoute", "asPathToSearchParams", "getRouteRegex", "adaptForAppRouterInstance", "router", "back", "forward", "refresh", "reload", "push", "href", "scroll", "undefined", "replace", "prefetch", "adaptForSearchParams", "isReady", "query", "URLSearchParams", "<PERSON><PERSON><PERSON>", "adaptForPathParams", "pathParams", "routeRegex", "pathname", "keys", "Object", "groups", "key", "PathnameContextProviderAdapter", "children", "props", "ref", "isAutoExport", "value", "current", "<PERSON><PERSON><PERSON><PERSON>", "url", "URL", "_", "Provider"], "mappings": "AAOA,OAAOA,SAASC,OAAO,EAAEC,MAAM,QAAQ,QAAO;AAC9C,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,cAAc,QAAQ,UAAS;AACxC,SAASC,oBAAoB,QAAQ,mCAAkC;AACvE,SAASC,aAAa,QAAQ,sBAAqB;AAEnD;;;;;CAKC,GACD,OAAO,SAASC,0BACdC,MAAkB;IAElB,OAAO;QACLC;YACED,OAAOC,IAAI;QACb;QACAC;YACEF,OAAOE,OAAO;QAChB;QACAC;YACEH,OAAOI,MAAM;QACf;QACAC,MAAKC,IAAY,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAmB,GAA3B,mBAA8B,CAAC,IAA/B;YACjB,KAAKP,OAAOK,IAAI,CAACC,MAAME,WAAW;gBAAED;YAAO;QAC7C;QACAE,SAAQH,IAAY,EAAE;YAAA,IAAA,EAAEC,MAAM,EAAmB,GAA3B,mBAA8B,CAAC,IAA/B;YACpB,KAAKP,OAAOS,OAAO,CAACH,MAAME,WAAW;gBAAED;YAAO;QAChD;QACAG,UAASJ,IAAY;YACnB,KAAKN,OAAOU,QAAQ,CAACJ;QACvB;IACF;AACF;AAEA;;;;;CAKC,GACD,OAAO,SAASK,qBACdX,MAAwD;IAExD,IAAI,CAACA,OAAOY,OAAO,IAAI,CAACZ,OAAOa,KAAK,EAAE;QACpC,OAAO,IAAIC;IACb;IAEA,OAAOjB,qBAAqBG,OAAOe,MAAM;AAC3C;AAEA,OAAO,SAASC,mBACdhB,MAAqE;IAErE,IAAI,CAACA,OAAOY,OAAO,IAAI,CAACZ,OAAOa,KAAK,EAAE;QACpC,OAAO;IACT;IACA,MAAMI,aAAqB,CAAC;IAC5B,MAAMC,aAAapB,cAAcE,OAAOmB,QAAQ;IAChD,MAAMC,OAAOC,OAAOD,IAAI,CAACF,WAAWI,MAAM;IAC1C,KAAK,MAAMC,OAAOH,KAAM;QACtBH,UAAU,CAACM,IAAI,GAAGvB,OAAOa,KAAK,CAACU,IAAI;IACrC;IACA,OAAON;AACT;AAEA,OAAO,SAASO,+BAA+B,KAO7C;IAP6C,IAAA,EAC7CC,QAAQ,EACRzB,MAAM,EACN,GAAG0B,OAIH,GAP6C;IAQ7C,MAAMC,MAAMjC,OAAOgC,MAAME,YAAY;IACrC,MAAMC,QAAQpC,QAAQ;QACpB,wEAAwE;QACxE,2EAA2E;QAC3E,iDAAiD;QACjD,MAAMmC,eAAeD,IAAIG,OAAO;QAChC,IAAIF,cAAc;YAChBD,IAAIG,OAAO,GAAG;QAChB;QAEA,sEAAsE;QACtE,qDAAqD;QACrD,IAAIlC,eAAeI,OAAOmB,QAAQ,GAAG;YACnC,yEAAyE;YACzE,uEAAuE;YACvE,MAAM;YACN,sFAAsF;YACtF,IAAInB,OAAO+B,UAAU,EAAE;gBACrB,OAAO;YACT;YAEA,oEAAoE;YACpE,wEAAwE;YACxE,mEAAmE;YACnE,mBAAmB;YACnB,0EAA0E;YAC1E,IAAIH,gBAAgB,CAAC5B,OAAOY,OAAO,EAAE;gBACnC,OAAO;YACT;QACF;QAEA,2EAA2E;QAC3E,2EAA2E;QAC3E,2BAA2B;QAC3B,kEAAkE;QAClE,IAAIoB;QACJ,IAAI;YACFA,MAAM,IAAIC,IAAIjC,OAAOe,MAAM,EAAE;QAC/B,EAAE,OAAOmB,GAAG;YACV,kDAAkD;YAClD,OAAO;QACT;QAEA,OAAOF,IAAIb,QAAQ;IACrB,GAAG;QAACnB,OAAOe,MAAM;QAAEf,OAAO+B,UAAU;QAAE/B,OAAOY,OAAO;QAAEZ,OAAOmB,QAAQ;KAAC;IAEtE,qBACE,oBAACxB,gBAAgBwC,QAAQ;QAACN,OAAOA;OAC9BJ;AAGP"}