{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/pages/builtin/_error.tsx"], "names": ["routeModule", "PagesRouteModule", "definition", "kind", "RouteKind", "PAGES", "page", "pathname", "filename", "bundlePath", "components", "App", "Document", "userland", "moduleError"], "mappings": ";;;;+BAQaA;;;eAAAA;;;iEARQ;4DACL;2BACU;+DAEG;+DAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMA,cAAc,IAAIC,eAAgB,CAAC;IAC9C,+CAA+C;IAC/CC,YAAY;QACVC,MAAMC,oBAAS,CAACC,KAAK;QACrBC,MAAM;QACNC,UAAU;QACVC,UAAU;QACVC,YAAY;IACd;IACAC,YAAY;QACVC,KAAAA,YAAG;QACHC,UAAAA,iBAAQ;IACV;IACAC,UAAUC;AACZ"}