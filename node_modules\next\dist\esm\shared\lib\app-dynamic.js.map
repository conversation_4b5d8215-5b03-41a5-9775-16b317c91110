{"version": 3, "sources": ["../../../src/shared/lib/app-dynamic.tsx"], "names": ["React", "Loadable", "convertModule", "mod", "default", "dynamic", "dynamicOptions", "options", "loadableFn", "loadableOptions", "loading", "error", "isLoading", "past<PERSON>elay", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "loader", "Object", "assign", "loaderFn", "then", "Promise", "resolve"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,cAAc,0BAAyB;AAyB9C,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cAAiBC,GAAgD;IACxE,OAAO;QAAEC,SAAS,CAACD,uBAAD,AAACA,IAA4BC,OAAO,KAAID;IAAI;AAChE;AAiBA,eAAe,SAASE,QACtBC,cAA6C,EAC7CC,OAA2B;IAE3B,MAAMC,aAA4BP;IAElC,MAAMQ,kBAAsC;QAC1C,wDAAwD;QACxDC,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIJ,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,oBAACM,WACEN,MAAMO,OAAO,gBACd,oBAACC,aACAR,MAAMS,KAAK;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,IAAI,OAAOd,mBAAmB,YAAY;QACxCG,gBAAgBY,MAAM,GAAGf;IAC3B;IAEAgB,OAAOC,MAAM,CAACd,iBAAiBF;IAE/B,MAAMiB,WAAWf,gBAAgBY,MAAM;IACvC,MAAMA,SAAS,IACbG,YAAY,OACRA,WAAWC,IAAI,CAACvB,iBAChBwB,QAAQC,OAAO,CAACzB,cAAc,IAAM;IAE1C,OAAOM,WAAW;QAAE,GAAGC,eAAe;QAAEY,QAAQA;IAAoB;AACtE"}