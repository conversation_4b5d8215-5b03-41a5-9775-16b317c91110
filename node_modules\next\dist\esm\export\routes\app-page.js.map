{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["isDynamicUsageError", "NEXT_CACHE_TAGS_HEADER", "NEXT_META_SUFFIX", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "hasNextSupport", "lazyRenderAppPage", "ExportedAppPageFiles", "HTML", "FLIGHT", "PREFETCH_FLIGHT", "META", "POSTPONED", "exportAppPage", "req", "res", "page", "path", "pathname", "query", "renderOpts", "htmlFilepath", "debugOutput", "isDynamicError", "fileWriter", "result", "html", "toUnchunkedString", "metadata", "flightData", "revalidate", "postponed", "fetchTags", "experimental", "ppr", "Error", "staticBailoutInfo", "description", "logDynamicUsageWarning", "stack", "replace", "headers", "meta", "status", "undefined", "JSON", "stringify", "hasEmptyPrelude", "Boolean", "hasPostponed", "err", "dynamicUsageDescription", "dynamicUsageStack", "store", "errMessage", "message", "substring", "indexOf", "console", "warn"], "mappings": "AASA,SAASA,mBAAmB,QAAQ,oCAAmC;AACvE,SACEC,sBAAsB,EACtBC,gBAAgB,EAChBC,mBAAmB,EACnBC,UAAU,QACL,sBAAqB;AAC5B,SAASC,cAAc,QAAQ,0BAAyB;AACxD,SAASC,iBAAiB,QAAQ,2DAA0D;WAErF;UAAWC,oBAAoB;IAApBA,qBAChBC,UAAAA;IADgBD,qBAEhBE,YAAAA;IAFgBF,qBAGhBG,qBAAAA;IAHgBH,qBAIhBI,UAAAA;IAJgBJ,qBAKhBK,eAAAA;GALgBL,yBAAAA;AAQlB,OAAO,eAAeM,cACpBC,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,QAAgB,EAChBC,KAAyB,EACzBC,UAAsB,EACtBC,YAAoB,EACpBC,WAAoB,EACpBC,cAAuB,EACvBC,UAAsB;IAEtB,6EAA6E;IAC7E,IAAIR,SAAS,eAAe;QAC1BE,WAAW;IACb;IAEA,IAAI;QACF,MAAMO,SAAS,MAAMnB,kBACnBQ,KACAC,KACAG,UACAC,OACAC;QAGF,MAAMM,OAAOD,OAAOE,iBAAiB;QAErC,MAAM,EAAEC,QAAQ,EAAE,GAAGH;QACrB,MAAM,EAAEI,UAAU,EAAEC,aAAa,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE,GAAGJ;QAEjE,uDAAuD;QACvD,IAAIG,aAAa,CAACX,WAAWa,YAAY,CAACC,GAAG,EAAE;YAC7C,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIL,eAAe,GAAG;YACpB,IAAIP,gBAAgB;gBAClB,MAAM,IAAIY,MACR,CAAC,+DAA+D,EAAElB,KAAK,CAAC,CAAC;YAE7E;YACA,MAAM,EAAEmB,oBAAoB,CAAC,CAAC,EAAE,GAAGR;YAEnC,IAAIE,eAAe,KAAKR,gBAAec,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrEC,uBAAuB;oBACrBrB;oBACAoB,aAAaD,kBAAkBC,WAAW;oBAC1CE,OAAOH,kBAAkBG,KAAK;gBAChC;YACF;YAEA,OAAO;gBAAET,YAAY;YAAE;QACzB,OAGK,IAAI,CAACD,YAAY;YACpB,MAAM,IAAIM,MAAM,CAAC,uCAAuC,EAAElB,KAAK,CAAC;QAClE,OAIK,IAAIG,WAAWa,YAAY,CAACC,GAAG,EAAE;YACpC,oEAAoE;YACpE,WAAW;YACX,MAAMV,WAvEQ,mBAyEZH,aAAamB,OAAO,CAAC,WAAWrC,sBAChC0B;QAEJ,OAAO;YACL,kEAAkE;YAClE,MAAML,WA/ED,UAiFHH,aAAamB,OAAO,CAAC,WAAWpC,aAChCyB;QAEJ;QAEA,MAAMY,UAAU;YAAE,GAAGb,SAASa,OAAO;QAAC;QAEtC,IAAIT,WAAW;YACbS,OAAO,CAACxC,uBAAuB,GAAG+B;QACpC;QAEA,iCAAiC;QACjC,MAAMR,WA9FD,QAgGHH,cACAK,QAAQ,IACR;QAGF,0CAA0C;QAC1C,MAAMgB,OAAsB;YAC1BC,QAAQC;YACRH;YACAV;QACF;QAEA,MAAMP,WAzGD,QA2GHH,aAAamB,OAAO,CAAC,WAAWtC,mBAChC2C,KAAKC,SAAS,CAACJ,MAAM,MAAM;QAG7B,OAAO;YACL,iEAAiE;YACjEd,UAAUvB,iBAAiBqC,OAAOE;YAClCG,iBAAiBC,QAAQjB,cAAcL,SAAS;YAChDuB,cAAcD,QAAQjB;YACtBD;QACF;IACF,EAAE,OAAOoB,KAAU;QACjB,IAAI,CAAClD,oBAAoBkD,MAAM;YAC7B,MAAMA;QACR;QAEA,IAAI5B,aAAa;YACf,MAAM,EAAE6B,uBAAuB,EAAEC,iBAAiB,EAAE,GAAG,AAAChC,WACrDiC,KAAK;YAERf,uBAAuB;gBACrBrB;gBACAoB,aAAac;gBACbZ,OAAOa;YACT;QACF;QAEA,OAAO;YAAEtB,YAAY;QAAE;IACzB;AACF;AAEA,SAASQ,uBAAuB,EAC9BrB,IAAI,EACJoB,WAAW,EACXE,KAAK,EAKN;IACC,MAAMe,aAAa,IAAInB,MACrB,CAAC,iDAAiD,EAAElB,KAAK,UAAU,EAAEoB,YAAY,CAAC;IAGpF,IAAIE,OAAO;QACTe,WAAWf,KAAK,GAAGe,WAAWC,OAAO,GAAGhB,MAAMiB,SAAS,CAACjB,MAAMkB,OAAO,CAAC;IACxE;IAEAC,QAAQC,IAAI,CAACL;AACf"}