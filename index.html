<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AL-ANSARI | Linktree</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://unpkg.com/framer-motion@10.16.16/dist/framer-motion.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
        }
        .card {
            background: rgba(31, 41, 55, 0.8);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(75, 85, 99, 1);
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
        }
        .link-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            font-size: 1.125rem;
            font-weight: 500;
            color: black;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        .link-item:hover {
            color: rgb(34, 197, 94);
        }
        .animate-scale {
            animation: scaleIn 0.8s ease-out;
        }
        .animate-fade-up {
            animation: fadeUp 0.6s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }
        @keyframes scaleIn {
            from {
                transform: scale(0);
            }
            to {
                transform: scale(1);
            }
        }
        @keyframes fadeUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-black via-gray-900 to-black text-white p-6">
    <!-- Logo -->
    <img src="public/ansari-logo.png" alt="Ansari Logo" class="w-32 h-32 mb-6 animate-scale">
    
    <!-- Title -->
    <h1 class="text-3xl font-bold mb-2 flex items-center gap-2 animate-fade-up" style="animation-delay: 0.3s;">
        AL-ANSARI 
        <i data-lucide="leaf" class="text-green-500"></i>
    </h1>
    
    <p class="text-gray-400 mb-8 text-center max-w-md animate-fade-up" style="animation-delay: 0.4s;">
        Welcome to my creative hub! Explore my work, projects, and insights across different platforms.
    </p>
    
    <!-- Links -->
    <div class="w-full max-w-md flex flex-col gap-4" id="links-container">
        <!-- Links will be populated by JavaScript -->
    </div>
    
    <!-- Footer -->
    <p class="text-gray-500 mt-10 text-sm animate-fade-up" style="animation-delay: 1.2s;">
        © <span id="current-year"></span> AL-ANSARI | Culture & Information
    </p>

    <script>
        // Set current year
        document.getElementById('current-year').textContent = new Date().getFullYear();
        
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Links data
        const links = [
            { href: "https://github.com/aliahmad1967", label: "GitHub", icon: "github" },
            { href: "https://public.tableau.com/app/profile/ali2526/vizzes", label: "Tableau Portfolio", icon: "bar-chart" },
            { href: "https://www.youtube.com/@nidalahmad6100", label: "YouTube", icon: "youtube" },
            { href: "https://huggingface.co/Ali1967", label: "Hugging Face", icon: "globe" },
            { href: "https://www.tiktok.com/@nidalahmad7", label: "TikTok", icon: "music" },
            { href: "https://x.com/nidal5nidal", label: "X (Twitter)", icon: "twitter" },
            { href: "https://www.linkedin.com/in/nidal-ahmad-a77881260", label: "LinkedIn", icon: "linkedin" },
            { href: "mailto:<EMAIL>", label: "Email", icon: "mail" }
        ];
        
        // Create links
        const linksContainer = document.getElementById('links-container');
        
        links.forEach((link, index) => {
            const linkDiv = document.createElement('div');
            linkDiv.className = 'animate-fade-up';
            linkDiv.style.animationDelay = `${0.5 + (index * 0.1)}s`;
            
            linkDiv.innerHTML = `
                <div class="card">
                    <a href="${link.href}" target="_blank" rel="noopener noreferrer" class="link-item">
                        <i data-lucide="${link.icon}"></i>
                        ${link.label}
                    </a>
                </div>
            `;
            
            linksContainer.appendChild(linkDiv);
        });
        
        // Re-initialize Lucide icons after adding new elements
        setTimeout(() => {
            lucide.createIcons();
        }, 100);
    </script>
</body>
</html>
