{"version": 3, "sources": ["../../../src/shared/lib/app-dynamic.tsx"], "names": ["dynamic", "convertModule", "mod", "default", "dynamicOptions", "options", "loadableFn", "Loadable", "loadableOptions", "loading", "error", "isLoading", "past<PERSON>elay", "process", "env", "NODE_ENV", "p", "message", "br", "stack", "loader", "Object", "assign", "loaderFn", "then", "Promise", "resolve"], "mappings": ";;;;+BAgDA;;;eAAwBA;;;;gEAhDN;mEACG;AAyBrB,yFAAyF;AACzF,qGAAqG;AACrG,qEAAqE;AACrE,SAASC,cAAiBC,GAAgD;IACxE,OAAO;QAAEC,SAAS,CAACD,uBAAD,AAACA,IAA4BC,OAAO,KAAID;IAAI;AAChE;AAiBe,SAASF,QACtBI,cAA6C,EAC7CC,OAA2B;IAE3B,MAAMC,aAA4BC,iBAAQ;IAE1C,MAAMC,kBAAsC;QAC1C,wDAAwD;QACxDC,SAAS;gBAAC,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;YACvC,IAAI,CAACA,WAAW,OAAO;YACvB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAIJ,WAAW;oBACb,OAAO;gBACT;gBACA,IAAID,OAAO;oBACT,qBACE,6BAACM,WACEN,MAAMO,OAAO,gBACd,6BAACC,aACAR,MAAMS,KAAK;gBAGlB;YACF;YACA,OAAO;QACT;IACF;IAEA,IAAI,OAAOf,mBAAmB,YAAY;QACxCI,gBAAgBY,MAAM,GAAGhB;IAC3B;IAEAiB,OAAOC,MAAM,CAACd,iBAAiBH;IAE/B,MAAMkB,WAAWf,gBAAgBY,MAAM;IACvC,MAAMA,SAAS,IACbG,YAAY,OACRA,WAAWC,IAAI,CAACvB,iBAChBwB,QAAQC,OAAO,CAACzB,cAAc,IAAM;IAE1C,OAAOK,WAAW;QAAE,GAAGE,eAAe;QAAEY,QAAQA;IAAoB;AACtE"}