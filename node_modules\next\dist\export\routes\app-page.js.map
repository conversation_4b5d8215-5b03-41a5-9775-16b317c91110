{"version": 3, "sources": ["../../../src/export/routes/app-page.ts"], "names": ["exportAppPage", "ExportedAppPageFiles", "HTML", "FLIGHT", "PREFETCH_FLIGHT", "META", "POSTPONED", "req", "res", "page", "path", "pathname", "query", "renderOpts", "htmlFilepath", "debugOutput", "isDynamicError", "fileWriter", "result", "lazyRenderAppPage", "html", "toUnchunkedString", "metadata", "flightData", "revalidate", "postponed", "fetchTags", "experimental", "ppr", "Error", "staticBailoutInfo", "description", "logDynamicUsageWarning", "stack", "replace", "RSC_PREFETCH_SUFFIX", "RSC_SUFFIX", "headers", "NEXT_CACHE_TAGS_HEADER", "meta", "status", "undefined", "NEXT_META_SUFFIX", "JSON", "stringify", "hasNextSupport", "hasEmptyPrelude", "Boolean", "hasPostponed", "err", "isDynamicUsageError", "dynamicUsageDescription", "dynamicUsageStack", "store", "errMessage", "message", "substring", "indexOf", "console", "warn"], "mappings": ";;;;;;;;;;;;;;;;;;IA2BsBA,aAAa;eAAbA;;;qCAlBc;2BAM7B;wBACwB;8BACG;IAE3B;UAAWC,oBAAoB;IAApBA,qBAChBC,UAAAA;IADgBD,qBAEhBE,YAAAA;IAFgBF,qBAGhBG,qBAAAA;IAHgBH,qBAIhBI,UAAAA;IAJgBJ,qBAKhBK,eAAAA;GALgBL,yBAAAA;AAQX,eAAeD,cACpBO,GAAkB,EAClBC,GAAmB,EACnBC,IAAY,EACZC,IAAY,EACZC,QAAgB,EAChBC,KAAyB,EACzBC,UAAsB,EACtBC,YAAoB,EACpBC,WAAoB,EACpBC,cAAuB,EACvBC,UAAsB;IAEtB,6EAA6E;IAC7E,IAAIR,SAAS,eAAe;QAC1BE,WAAW;IACb;IAEA,IAAI;QACF,MAAMO,SAAS,MAAMC,IAAAA,+BAAiB,EACpCZ,KACAC,KACAG,UACAC,OACAC;QAGF,MAAMO,OAAOF,OAAOG,iBAAiB;QAErC,MAAM,EAAEC,QAAQ,EAAE,GAAGJ;QACrB,MAAM,EAAEK,UAAU,EAAEC,aAAa,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE,GAAGJ;QAEjE,uDAAuD;QACvD,IAAIG,aAAa,CAACZ,WAAWc,YAAY,CAACC,GAAG,EAAE;YAC7C,MAAM,IAAIC,MAAM;QAClB;QAEA,IAAIL,eAAe,GAAG;YACpB,IAAIR,gBAAgB;gBAClB,MAAM,IAAIa,MACR,CAAC,+DAA+D,EAAEnB,KAAK,CAAC,CAAC;YAE7E;YACA,MAAM,EAAEoB,oBAAoB,CAAC,CAAC,EAAE,GAAGR;YAEnC,IAAIE,eAAe,KAAKT,gBAAee,qCAAAA,kBAAmBC,WAAW,GAAE;gBACrEC,uBAAuB;oBACrBtB;oBACAqB,aAAaD,kBAAkBC,WAAW;oBAC1CE,OAAOH,kBAAkBG,KAAK;gBAChC;YACF;YAEA,OAAO;gBAAET,YAAY;YAAE;QACzB,OAGK,IAAI,CAACD,YAAY;YACpB,MAAM,IAAIM,MAAM,CAAC,uCAAuC,EAAEnB,KAAK,CAAC;QAClE,OAIK,IAAIG,WAAWc,YAAY,CAACC,GAAG,EAAE;YACpC,oEAAoE;YACpE,WAAW;YACX,MAAMX,WAvEQ,mBAyEZH,aAAaoB,OAAO,CAAC,WAAWC,8BAAmB,GACnDZ;QAEJ,OAAO;YACL,kEAAkE;YAClE,MAAMN,WA/ED,UAiFHH,aAAaoB,OAAO,CAAC,WAAWE,qBAAU,GAC1Cb;QAEJ;QAEA,MAAMc,UAAU;YAAE,GAAGf,SAASe,OAAO;QAAC;QAEtC,IAAIX,WAAW;YACbW,OAAO,CAACC,iCAAsB,CAAC,GAAGZ;QACpC;QAEA,iCAAiC;QACjC,MAAMT,WA9FD,QAgGHH,cACAM,QAAQ,IACR;QAGF,0CAA0C;QAC1C,MAAMmB,OAAsB;YAC1BC,QAAQC;YACRJ;YACAZ;QACF;QAEA,MAAMR,WAzGD,QA2GHH,aAAaoB,OAAO,CAAC,WAAWQ,2BAAgB,GAChDC,KAAKC,SAAS,CAACL,MAAM,MAAM;QAG7B,OAAO;YACL,iEAAiE;YACjEjB,UAAUuB,sBAAc,GAAGN,OAAOE;YAClCK,iBAAiBC,QAAQtB,cAAcL,SAAS;YAChD4B,cAAcD,QAAQtB;YACtBD;QACF;IACF,EAAE,OAAOyB,KAAU;QACjB,IAAI,CAACC,IAAAA,wCAAmB,EAACD,MAAM;YAC7B,MAAMA;QACR;QAEA,IAAIlC,aAAa;YACf,MAAM,EAAEoC,uBAAuB,EAAEC,iBAAiB,EAAE,GAAG,AAACvC,WACrDwC,KAAK;YAERrB,uBAAuB;gBACrBtB;gBACAqB,aAAaoB;gBACblB,OAAOmB;YACT;QACF;QAEA,OAAO;YAAE5B,YAAY;QAAE;IACzB;AACF;AAEA,SAASQ,uBAAuB,EAC9BtB,IAAI,EACJqB,WAAW,EACXE,KAAK,EAKN;IACC,MAAMqB,aAAa,IAAIzB,MACrB,CAAC,iDAAiD,EAAEnB,KAAK,UAAU,EAAEqB,YAAY,CAAC;IAGpF,IAAIE,OAAO;QACTqB,WAAWrB,KAAK,GAAGqB,WAAWC,OAAO,GAAGtB,MAAMuB,SAAS,CAACvB,MAAMwB,OAAO,CAAC;IACxE;IAEAC,QAAQC,IAAI,CAACL;AACf"}