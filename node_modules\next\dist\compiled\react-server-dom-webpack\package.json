{"name": "react-server-dom-webpack-builtin", "main": "index.js", "exports": {".": "./index.js", "./plugin": "./plugin.js", "./client": {"workerd": "./client.edge.js", "deno": "./client.edge.js", "worker": "./client.edge.js", "node": {"webpack": "./client.node.js", "default": "./client.node.unbundled.js"}, "edge-light": "./client.edge.js", "browser": "./client.browser.js", "default": "./client.browser.js"}, "./client.browser": "./client.browser.js", "./client.edge": "./client.edge.js", "./client.node": "./client.node.js", "./client.node.unbundled": "./client.node.unbundled.js", "./server": {"react-server": {"workerd": "./server.edge.js", "deno": "./server.browser.js", "node": {"webpack": "./server.node.js", "default": "./server.node.unbundled.js"}, "edge-light": "./server.edge.js", "browser": "./server.browser.js"}, "default": "./server.js"}, "./server.browser": "./server.browser.js", "./server.edge": "./server.edge.js", "./server.node": "./server.node.js", "./server.node.unbundled": "./server.node.unbundled.js", "./node-loader": "./esm/react-server-dom-webpack-node-loader.production.min.js", "./node-register": "./node-register.js", "./package.json": "./package.json"}, "dependencies": {"acorn-loose": "^8.3.0", "neo-async": "^2.6.1", "loose-envify": "^1.1.0"}, "peerDependencies": {"react": "18.3.0-canary-2c338b16f-20231116", "react-dom": "18.3.0-canary-2c338b16f-20231116", "webpack": "^5.59.0"}}