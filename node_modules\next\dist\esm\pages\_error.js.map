{"version": 3, "sources": ["../../src/pages/_error.tsx"], "names": ["React", "Head", "statusCodes", "_getInitialProps", "res", "err", "statusCode", "styles", "error", "fontFamily", "height", "textAlign", "display", "flexDirection", "alignItems", "justifyContent", "desc", "lineHeight", "h1", "margin", "paddingRight", "fontSize", "fontWeight", "verticalAlign", "h2", "wrap", "Error", "Component", "render", "withDarkMode", "props", "title", "div", "style", "dangerouslySetInnerHTML", "__html", "className", "displayName", "getInitialProps", "origGetInitialProps"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AACzB,OAAOC,UAAU,qBAAoB;AAGrC,MAAMC,cAA0C;IAC9C,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAQA,SAASC,iBAAiB,KAGR;IAHQ,IAAA,EACxBC,GAAG,EACHC,GAAG,EACa,GAHQ;IAIxB,MAAMC,aACJF,OAAOA,IAAIE,UAAU,GAAGF,IAAIE,UAAU,GAAGD,MAAMA,IAAIC,UAAU,GAAI;IACnE,OAAO;QAAEA;IAAW;AACtB;AAEA,MAAMC,SAA8C;IAClDC,OAAO;QACL,0FAA0F;QAC1FC,YACE;QACFC,QAAQ;QACRC,WAAW;QACXC,SAAS;QACTC,eAAe;QACfC,YAAY;QACZC,gBAAgB;IAClB;IACAC,MAAM;QACJC,YAAY;IACd;IACAC,IAAI;QACFN,SAAS;QACTO,QAAQ;QACRC,cAAc;QACdC,UAAU;QACVC,YAAY;QACZC,eAAe;IACjB;IACAC,IAAI;QACFH,UAAU;QACVC,YAAY;QACZL,YAAY;IACd;IACAQ,MAAM;QACJb,SAAS;IACX;AACF;AAKe,MAAMc,cAAsB1B,MAAM2B,SAAS;IAMxDC,SAAS;QACP,MAAM,EAAEtB,UAAU,EAAEuB,eAAe,IAAI,EAAE,GAAG,IAAI,CAACC,KAAK;QACtD,MAAMC,QACJ,IAAI,CAACD,KAAK,CAACC,KAAK,IAChB7B,WAAW,CAACI,WAAW,IACvB;QAEF,qBACE,oBAAC0B;YAAIC,OAAO1B,OAAOC,KAAK;yBACtB,oBAACP,0BACC,oBAAC8B,eACEzB,aACG,AAAGA,aAAW,OAAIyB,QAClB,2EAGR,oBAACC;YAAIC,OAAO1B,OAAOS,IAAI;yBACrB,oBAACiB;YACCC,yBAAyB;gBACvB;;;;;;;;;;;;;;;;eAgBC,GACDC,QAAQ,AAAC,mGACPN,CAAAA,eACI,oIACA,EAAC;YAET;YAGDvB,2BACC,oBAACY;YAAGkB,WAAU;YAAgBH,OAAO1B,OAAOW,EAAE;WAC3CZ,cAED,oBACJ,oBAAC0B;YAAIC,OAAO1B,OAAOkB,IAAI;yBACrB,oBAACD;YAAGS,OAAO1B,OAAOiB,EAAE;WACjB,IAAI,CAACM,KAAK,CAACC,KAAK,IAAIzB,aACnByB,sBAEA,0CAAE,2GAIF;IAOd;AACF;AAxEqBL,MACZW,cAAc;AADFX,MAGZY,kBAAkBnC;AAHNuB,MAIZa,sBAAsBpC;AAP/B;;CAEC,GACD,SAAqBuB,mBAwEpB"}