["@aws-sdk/client-s3", "@aws-sdk/s3-presigned-post", "@blockfrost/blockfrost-js", "@highlight-run/node", "@libsql/client", "@jpg-store/lucid-cardano", "@mikro-orm/core", "@mikro-orm/knex", "@prisma/client", "@sentry/nextjs", "@sentry/node", "@swc/core", "argon2", "autoprefixer", "aws-crt", "bcrypt", "better-sqlite3", "canvas", "cpu-features", "cypress", "eslint", "express", "firebase-admin", "jest", "jsdom", "libsql", "lodash", "mdx-bundler", "mongodb", "mongoose", "next-mdx-remote", "next-seo", "node-pty", "payload", "pg", "playwright", "postcss", "prettier", "prisma", "puppeteer", "<PERSON><PERSON><PERSON>", "sharp", "shiki", "sqlite3", "tailwindcss", "ts-node", "typescript", "vscode-oniguruma", "webpack", "websocket"]